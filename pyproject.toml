[project]
name = "widget-backend"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
     "argon2-cffi>=23.1.0",
     "bcrypt==4.2.1",
     "fastapi[standard]>=0.115.11",
     "httpx>=0.28.1",
     "pyjwt>=2.10.1",
     "pymongo>=4.11.2",
     "pytest>=8.3.5",
     "pytest-asyncio>=0.25.3",
     "pytest-cov>=6.0.0",
     "pytest-mongodb>=2.4.0",
     "python-dotenv>=1.0.1",
     "python-multipart>=0.0.20",
     "uvicorn>=0.34.0",
     "llama-index>=0.12.42",
     "llama-index-vector-stores-qdrant>=0.6.0",
     "openai>=1.86.0",
     "pymongo>=4.13.1",
     "argon2-cffi-bindings==21.2.0",
     "passlib==1.7.4",
     "google-generativeai>=0.8.5",
     "google-genai>=1.20.0",
     "google>=3.0.0",
     "trafilatura>=2.0.0",
]
