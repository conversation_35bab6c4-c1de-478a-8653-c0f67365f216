
# 📰 TechPana RAG API

A FastAPI-powered backend for receiving Nepali tech news posts and generating rich, AI-powered summaries and Q&A through a Retrieval-Augmented Generation (RAG) pipeline.

---

## 📬 Webhook Endpoint

### `POST /v1/webhook/receive`

Receives raw Nepali news posts and triggers the full processing pipeline: cleaning → segmenting → embedding → storing → generating summaries and QA pairs.

#### 🟢 Request Body (application/json)

```json
[
  {
    "id": "123456",
    "title_nepali": "फास्ट चार्जिङले फोन प्रयोगमा कस्तो असर पाउँछ ?",
    "body_nepali": "\n<p class=\"has-text-align-justify\">फास्ट चार्जिङले फोन छिटो चार्ज गर्न सहयोग गर्छ...",
    "category": "प्रविधि",
    "secondary_categories": ["मोबाइल", "ब्याट्री"],
    "author": "techpana",
    "event_date": "2025-06-19"
  }
]
```

#### ✅ Response

```json
{
  "message": "Post received and processing started",
  "post_id": "123456"
}
```

---

## 🔍 RAG Retrieval Endpoint

### `POST /v1/rag/generate`

Given a `post_id`, this endpoint runs a RAG pipeline that generates:
- A concise English summary of the post
- Related article discovery
- Comparative Q&A pairs based on semantic relevance

#### 🟢 Request Body (application/json)

```json
{
  "post_id": "123456"
}
```

#### ✅ Response Schema

```json
{
  "post_id": "123456",
  "main_post_summary": "• Fast charging helps speed up mobile usage...\n• However, it may have long-term effects on battery health...",
  "related_posts": {
    "abc123": {
      "post_id": "abc123",
      "url": "https://techpana.com/posts/abc123",
      "score": 0.84,
      "relevance_summary": "Discusses battery degradation in fast-charging phones...",
      "qa_pairs": [
        {
          "question": "How does battery wear differ between fast and slow charging?",
          "answer": "Fast charging increases heat, which accelerates battery aging compared to slower methods."
        },
        {
          "question": "Are there phone brands more resistant to fast charging degradation?",
          "answer": "Yes, some manufacturers like XYZ include better thermal management systems to handle fast charging."
        }
      ]
    }
  }
}
```

---

## 🛠️ Tech Stack

- **FastAPI** – REST framework
- **MongoDB** – Raw and processed article storage
- **Qdrant** – Vector similarity search
- **OpenAI / Gemini** – LLM-based summarization and Q&A generation


