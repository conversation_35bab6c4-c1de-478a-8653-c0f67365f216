version: '3.8'

services:
  app:
    image: techpana:latest
    networks:
      - techpana_bridge_net

  nginx:
    image: nginx:alpine
    container_name: nginx_proxy
    ports:
      - "8010:443"
    volumes:
      - ./etc/nginx/conf.d/techpana-ai.nextai.asia.conf:/etc/nginx/conf.d/techpana-ai.nextai.asia.conf:ro
      - /etc/letsencrypt/live/techpana-ai.nextai.asia:/etc/letsencrypt/live/techpana-ai.nextai.asia:ro
    depends_on:
      - app
    networks:
      - techpana_bridge_net

networks:
  techpana_bridge_net:
    driver: bridge