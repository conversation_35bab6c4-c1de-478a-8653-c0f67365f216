from fastapi import APIRouter, HTTPException, Depends
from app.models.user import UserTenantDB
from typing import List
import os 
from app.v1.api.techpana.core import shared_state
from app.v1.api.techpana.webhook.models import IncomingPost
from app.v1.api.techpana.webhook.controller import ResponsePopulate
from app.core.security import  get_tenant_info, require_permissions

router = APIRouter(prefix = "", tags=["Webhook"])
mongo_collection = os.getenv('MONGODB_COLLECTION_NAME')



@router.post("/webhook")
async def receive_posts(
    posts: List[IncomingPost],
    current_user: UserTenantDB = Depends(require_permissions(["write_post"]))
):
    try:
        # ✅ Initialize the class
        processor = ResponsePopulate()
        results = []
        for post in posts:
            post_data = post.dict()
            result = await processor.process_post_and_generate(post_data)
            results.append({
                "post_id": post.id,
                "result": result
            })
        return {"status": "success", "results": results}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))



@router.post("/webhook/trigger-rag")
async def trigger_rag_update(
    current_user: UserTenantDB = Depends(require_permissions(["write_post"]))
):
    """
    Trigger QA + Summary generation on all unprocessed documents in MongoDB.
    """
    try:
        processor = ResponsePopulate()

        await processor.check_post(update_all=True)
        return {"status": "success", "message": "RAG update triggered for all documents."}

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"RAG update failed: {str(e)}")