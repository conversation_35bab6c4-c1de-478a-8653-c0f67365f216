from pydantic import BaseModel, Field
from typing import Dict, Optional, Any

class IncomingPost(BaseModel):
    id: str = Field(..., example="123456")
    title_nepali: str = Field(..., example="फास्ट चार्जिङले फोन प्रयोगमा कस्तो असर पार्छ ?")
    body_nepali: str = Field(..., example="\n<p class=\"has-text-align-justify\">फास्ट चार्जिङले फोन छिटो चार्ज गर्न सहयोग गर्छ तर यसले ब्याट्रीको आयुमा असर पार्न सक्छ भन्ने बहस पनि चलिरहेको छ।</p>\n\n\n\n")
    category: Optional[str] = Field(None, example="प्रविधि")
    secondary_categories: Optional[str] = Field(None, example="मोबाइल, बैट्री")
    author: Optional[Any] = Field(None, example = "techpana")
    event_date: Optional[Any] = Field(None, example="2025-06-19")

