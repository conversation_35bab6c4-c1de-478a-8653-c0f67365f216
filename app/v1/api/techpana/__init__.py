from fastapi import FastAPI, APIRouter
from app.v1.api.techpana.api import router as api_router
from app.v1.api.techpana.webhook import router as webhook_router
from contextlib import asynccontextmanager
from app.v1.api.techpana.rag.config import RAGConfig
from app.v1.api.techpana.core import shared_state
from app.v1.api.techpana.rag.response_generator import ResponseGenerator
import logging
import traceback

logger = logging.getLogger(__name__)

router = APIRouter()


router.include_router(api_router)
router.include_router(webhook_router)  

# Lifespan for startup/shutdown logic
@asynccontextmanager
async def lifespan(app: FastAPI):
    global generator
    try:
        logger.info("🚀 Starting RAG initialization...")
        
        # Initialize config first
        shared_state.rag_config = RAGConfig()
        logger.info(f"✅ RAG Config initialized")
        logger.info(f"   - MongoDB URI: {shared_state.rag_config.mongo_uri}")
        logger.info(f"   - Database: {shared_state.rag_config.mongo_db_name}")
        logger.info(f"   - Collection: {shared_state.rag_config.mongo_collection_name}")
        
        # Validate config values
        if not shared_state.rag_config.mongo_uri:
            raise ValueError("MongoDB URI is not set in config")
        if not shared_state.rag_config.mongo_db_name:
            raise ValueError("MongoDB database name is not set in config")
        if not shared_state.rag_config.mongo_collection_name:
            raise ValueError("MongoDB collection name is not set in config")
        
        # Initialize generator
        logger.info("🔧 Initializing ResponseGenerator...")
        shared_state.generator = ResponseGenerator(
            mongo_uri=shared_state.rag_config.mongo_uri,
            mongo_db=shared_state.rag_config.mongo_db_name,
            mongo_collection=shared_state.rag_config.mongo_collection_name
        )
        logger.info("✅ Generator initialized successfully!")
        
    except Exception as e:
        logger.error(f"❌ Failed to initialize generator: {str(e)}")
        logger.error(f"❌ Full traceback: {traceback.format_exc()}")
        generator = None
    
    yield