from qdrant_client import QdrantClient
from qdrant_client.http import models
from llama_index.vector_stores.qdrant import QdrantVectorStore
from llama_index.embeddings.openai import OpenAIEmbedding
from llama_index.core.schema import Document
from llama_index.core import VectorStoreIndex
from typing import Tuple, Any, List, Dict
from pymongo import MongoClient
from tqdm import tqdm
import json
import uuid
import logging
import os

from .config import RAGConfig

logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO)


class VectorStoreManager:
    def __init__(
        self,
        host: str = None,
        port: int = None,
        collection_name: str = None,
        embedding_model: str = None,
        mongo_uri: str = None,
        mongo_db: str = None,
        mongo_collection: str = None
    ):
        config = RAGConfig()
        self.client = QdrantClient(
            host=config.qdrant_host,
            port=config.qdrant_port,
            https=False,
            timeout=60
        )
        self.collection_name = config.collection_name
        self.embed_model = OpenAIEmbedding(model_name=config.embedding_model)

        self.mongo_uri = mongo_uri
        self.mongo_db = mongo_db
        self.mongo_collection = mongo_collection
        self.mongo_client = MongoClient(self.mongo_uri)
        self.db = self.mongo_client[self.mongo_db]
        self.collection = self.db[self.mongo_collection]

    def is_collection_empty(self) -> bool:
        try:
            self.client.get_collection(self.collection_name)
            count = self.client.count(collection_name=self.collection_name, exact=True).count
            return count == 0
        except Exception:
            return True

    def initialize_collection(self, vector_size: int):
        logger.info(f"Initializing Qdrant collection '{self.collection_name}' with vector size {vector_size}")
        self.client.recreate_collection(
            collection_name=self.collection_name,
            vectors_config=models.VectorParams(size=vector_size, distance=models.Distance.COSINE)
        )

    def get_vector_store(self) -> QdrantVectorStore:
        return QdrantVectorStore(client=self.client, collection_name=self.collection_name)

    def get_embedding_model(self):
        return self.embed_model

    def load_data_from_mongo(self) -> List[Dict]:
        # Load only unprocessed posts
        data = list(self.collection.find({"embedded": {"$ne": True}}))
        logger.info(f"✅ Loaded {len(data)} new articles from MongoDB.")
        return data

    def populate_vector_store(self, data: List[Dict]):
        all_documents = []
        incomplete_articles = []

        logger.info("Starting to process documents and generate LlamaIndex Documents...")

        total_items = sum(
            sum(1 for _, text in doc.get("article_content", {}).items() if isinstance(text, str) and text.strip())
            for doc in data
        )

        with tqdm(total=total_items, desc="Creating LlamaIndex Documents") as pbar:
            for entry in data:
                post_id = entry.get("post_id")
                title = entry.get("title_nepali")
                category = entry.get("category")
                url = entry.get("post_url")
                content = entry.get("article_content", {})

                if all(isinstance(v, str) and not v.strip() for v in content.values()):
                    incomplete_articles.append({
                        "post_id": post_id,
                        "title": title,
                        "category": category,
                        "post_url": url,
                        "article_content": content
                    })
                    continue

                for section, text in content.items():
                    if isinstance(text, str) and text.strip():
                        doc_id = str(uuid.uuid4())
                        try:
                            doc = Document(
                                text=text.strip(),
                                metadata={
                                    "ref_post_id": post_id,
                                    "category": category,
                                    "title_nepali": title,
                                    "post_url": url,
                                    "section": section,
                                    "level": section,
                                    "doc_type": "content_chunk",
                                    "document_id": doc_id
                                },
                                id_=doc_id
                            )
                            all_documents.append(doc)
                            pbar.update(1)
                        except Exception as e:
                            logger.error(f"Error creating Document for {post_id}:{section} — {str(e)}")
                            continue

        logger.info(f"🔢 Total LlamaIndex Documents created: {len(all_documents)}")
        logger.info(f"🛑 Total incomplete articles: {len(incomplete_articles)}")

        # if incomplete_articles:
        #     try:
        #         output_path = os.path.join(os.path.dirname(__file__), "incomplete_articles.json")
        #         with open(output_path, "w", encoding="utf-8") as f:
        #             json.dump(incomplete_articles, f, ensure_ascii=False, indent=2)
        #         logger.info(f"Saved incomplete articles to: {output_path}")
        #     except Exception as e:
        #         logger.error(f"❌ Failed to save incomplete articles: {str(e)}")

        if not all_documents:
            logger.warning("⚠️ No valid documents to insert.")
            return

        try:
            index = VectorStoreIndex.from_vector_store(self.get_vector_store(), embed_model=self.get_embedding_model())
            batch_size = 100
            for i in tqdm(range(0, len(all_documents), batch_size), desc="Inserting into Qdrant"):
                batch = all_documents[i:i + batch_size]
                try:
                    index.insert_nodes(batch)
                    logger.info(f"✅ Inserted batch {i // batch_size + 1} ({len(batch)} docs)")
                    post_ids = list({doc.metadata["ref_post_id"] for doc in batch})
                    self.collection.update_many({"post_id": {"$in": post_ids}}, {"$set": {"embedded": True}})
                except Exception as e:
                    logger.error(f"❌ Batch insert failed: {str(e)}")
        except Exception as e:
            logger.error(f"❌ Error setting up VectorStoreIndex: {str(e)}")

    def setup_and_populate(self, force_recreate: bool = False) -> Tuple[bool, Any]:
        if force_recreate:
            self.initialize_collection(vector_size=3072)
        else:
            try:
                self.client.get_collection(self.collection_name)
                logger.info("✅ Qdrant collection already exists. Skipping recreation.")
            except Exception:
                logger.warning("⚠️ Qdrant collection not found. Initializing new one.")
                self.initialize_collection(vector_size=3072)

        data = self.load_data_from_mongo()
        if data:
            self.populate_vector_store(data)
            return True, data
        else:
            logger.info("No new documents to insert.")
            return False, None
