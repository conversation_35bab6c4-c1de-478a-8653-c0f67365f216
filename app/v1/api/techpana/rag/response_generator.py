from pymongo import AsyncMongoClient
import time
from fastapi import <PERSON><PERSON>P<PERSON>x<PERSON>
from typing import Dict, Any
from openai import AsyncOpenAI
from app.v1.api.techpana.rag.retrievers.retriever import TraditionalRetriever
from app.v1.api.techpana.rag.pipeline.hasher import PostHasher
import logging
from tqdm import tqdm
import asyncio
logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO)

class ResponseGenerator:
    def __init__(self, mongo_uri: str, mongo_db: str, mongo_collection: str):
        self.mongo_uri = mongo_uri
        self.mongo_db = mongo_db
        self.mongo_collection = mongo_collection
        

        try:
            self.llm = AsyncOpenAI()  # Ensure OPENAI_API_KEY is set
            self.traditional_retriever = TraditionalRetriever()
        except Exception as e:
            logger.error(f"Failed to initialize ResponseGenerator: {str(e)}")
            raise

    async def traditional_retrieve_full(
        self, post_id: str
    ) -> Dict[str, Any]:
        """
        Wrapper to invoke TraditionalRetriever with external MongoDB params.
        """
        try:
            result = await self.traditional_retriever.retrieve_full(
                post_id=post_id,
                mongo_uri=self.mongo_uri,
                mongo_db=self.mongo_db,
                mongo_collection=self.mongo_collection
            )
            return result

        except Exception as e:
            logger.error(f"Error in traditional_retrieve_full for post_id {post_id}: {str(e)}")
            raise

    # async def generate_from_post_id(self, post_id: str) -> dict:
    #     start_time = time.time()

    #     tools = [
    #         {
    #             "type": "function",
    #             "function": {
    #                 "name": "traditional_retrieve_full",
    #                 "description": (
    #                     "Retrieve and summarize article + generate questions based on keywords "
    #                     "and retrieve related posts for the given post_id from the specified MongoDB database."
    #                 ),
    #                 "parameters": {
    #                     "type": "object",
    #                     "properties": {
    #                         "post_id": {
    #                             "type": "string",
    #                             "description": "The ID of the post to retrieve."
    #                         },
    #                     },
    #                     "required": ["post_id"]
    #                 }
    #             },
    #             "strict": True
    #         }
    #     ]

    #     try:
    #         response = await self.llm.chat.completions.create(
    #             model="gpt-4.1-mini-2025-04-14",
    #             messages=[
    #                 {
    #                     "role": "system",
    #                     "content": (
    #                         "Use the tool to retrieve and summarize the main post, "
    #                         "generate comparative Q&A pairs based on keywords, "
    #                         "and retrieve related posts with relevance summaries."
    #                     )
    #                 },
    #                 {
    #                     "role": "user",
    #                     "content": f"Generate insights for post ID: {post_id}"
    #                 }
    #             ],
    #             tools=tools,
    #             tool_choice="auto"
    #         )
    #     except Exception as e:
    #         logger.error(f"OpenAI API call failed: {str(e)}")
    #         return {
    #             "post_id": post_id,
    #             "error": f"Failed to call OpenAI API: {str(e)}",
    #             "retrieval_time": time.time() - start_time
    #         }

    #     message = response.choices[0].message

    #     if not message.tool_calls:
    #         logger.warning("No tool call was made by the model.")
    #         return {
    #             "post_id": post_id,
    #             "error": "No tool call was made by the model.",
    #             "retrieval_time": time.time() - start_time
    #         }

    #     tool_call = message.tool_calls[0]
    #     if tool_call.function.name != "traditional_retrieve_full":
    #         logger.warning(f"Unexpected tool called: {tool_call.function.name}")
    #         return {
    #             "post_id": post_id,
    #             "error": f"Unexpected tool called: {tool_call.function.name}",
    #             "retrieval_time": time.time() - start_time
    #         }

    #     try:
    #         fn_args = json.loads(tool_call.function.arguments)
    #         if "post_id" not in fn_args:
    #             raise ValueError("Missing 'post_id' in tool call arguments.")
    #     except (json.JSONDecodeError, ValueError) as e:
    #         logger.error(f"Invalid tool call arguments: {str(e)}")
    #         return {
    #             "post_id": post_id,
    #             "error": f"Invalid tool call arguments: {str(e)}",
    #             "retrieval_time": time.time() - start_time
    #         }

    #     try:
    #         result = await self.traditional_retrieve_full(post_id=fn_args["post_id"])
    #         elapsed_time = time.time() - start_time
    #         return {
    #             "post_id": fn_args["post_id"],
    #             "main_post_summary": result.get("main_post_summary", "No summary generated"),
    #             "related_posts": result.get("related_posts", {}),
    #             "retrieval_time": elapsed_time
    #         }

    #     except Exception as e:
    #         logger.error(f"Error calling traditional_retrieve_full: {str(e)}")
    #         return {
    #             "post_id": fn_args.get("post_id", "unknown"),
    #             "error": f"Error generating response: {str(e)}",
    #             "retrieval_time": time.time() - start_time
    #         }


    async def generate_from_post_id(self, post_id: str) -> dict:
        start_time = time.time()

        try:
            result = await self.traditional_retrieve_full(post_id=post_id)
            elapsed_time = time.time() - start_time
            return {
                "post_id": post_id,
                "main_post_summary": result.get("main_post_summary", "No summary generated"),
                "related_posts": result.get("related_posts", []),
                "retrieval_time": elapsed_time
            }


        except Exception as e:
            logger.error(f"Error calling traditional_retrieve_full: {str(e)}")
            return {
                "post_id": post_id,
                "error": f"Error generating response: {str(e)}",
                "retrieval_time": time.time() - start_time
            }
        


    async def get_report_from_post_id(self, post_id: str) -> dict:
        client = AsyncMongoClient(self.mongo_uri)
        self.collection = client[self.mongo_db][self.mongo_collection]
        post_id = PostHasher.hash_post_id(post_id)
        try:
            document = await self.collection.find_one(
                {"post_id": post_id},
                {"_id": 0, "main_post_summary": 1, "related_posts": 1}
            )

            if not document:
                raise HTTPException(status_code=404, detail=f"Post with post_id '{post_id}' not found.")

            return document

        except Exception as e:
            # Raise a 500 error for any unexpected issue
            raise HTTPException(status_code=500, detail=f"Error retrieving report: {str(e)}")
