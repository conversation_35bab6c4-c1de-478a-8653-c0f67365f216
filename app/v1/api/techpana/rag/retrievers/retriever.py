from typing import List, Dict, Any
from .base_retriever import BaseRetriever
from collections import defaultdict
from llama_index.core.schema import NodeWithScore
from llama_index.core.vector_stores import VectorStoreQuery, MetadataFilters, MetadataFilter
import google.generativeai as genai
from app.v1.api.techpana.rag.pipeline.hasher import PostHasher
from app.v1.api.techpana.rag.config import RAGConfig
from pymongo import AsyncMongoClient
import os
import logging
import json
import asyncio

logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO)

class TraditionalRetriever(BaseRetriever):
    def __init__(self):
        self.config = RAGConfig()
        self.index = self.config.index
        self.embedding_model = self.config._embed_model
        # Configure Gemini API
        genai.configure(api_key=os.getenv("GEMINI_API_KEY"))
        self.model = genai.GenerativeModel(
            model_name="gemini-1.5-flash",
            generation_config={"response_mime_type": "text/plain"}
        )

    async def summarize_segments(self, segments: List[dict]) -> str:
        if not segments:
            return "• No segments for summary."

        context = "\n\n".join(f"[{seg.get('section', 'unknown')}]\n{seg.get('text', '')}" for seg in segments)
        prompt = (
            "You are given multiple segments of a Nepali news article.\n\n"
            "Your task is to generate a concise summary in Nepali using 2 to 3 short bullet points.\n"
            "Each bullet point should:\n"
            "• Begin with a bullet symbol (•)\n"
            "• Be written in one short, clear sentence\n"
            "• Cover the key points across all segments without repetition\n\n"
            "Write only the summary:\n\n"
            f"{context[:2000]}"
)

        try:
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                None,
                lambda: self.model.generate_content(
                    contents=[{"role": "user", "parts": [{"text": prompt}]}],
                    generation_config={"response_mime_type": "text/plain"}
                )
            )
            return response.text.strip()
        except Exception as e:
            logger.error(f"Error in summarize_segments: {e}", exc_info=True)
            return "• सारांश तयार गर्ने क्रममा त्रुटि भयो।"

    async def generate_questions(self, main_summary: str, related_text: str) -> List[Dict[str, str]]:
        prompt = f"""
        You are given summaries of two Nepali tech news articles:

        🔸 Summary 1:
        {main_summary}

        🔸 Summary 2:
        {related_text}

        Your task is to generate 4 to 6 comparative and contextually relevant questions based on specific overlaps or contrasts in:
        • companies
        • technologies
        • services
        • locations
        • or policies

        ❗ Strict Instructions:
        - Do NOT use phrases like "Summary 1" or "Summary 2".
        - Use the actual names mentioned in the summaries (e.g., Esewa, Samsung, Nepal Investment Bank etc.).
        - Base all questions strictly on the provided information — do NOT guess or hallucinate.
        - Each answer must be 1–2 lines long and written in clear Nepali.
        - Only include relevant comparisons; if no meaningful comparison exists, skip.

        📋 Respond ONLY in the following exact JSON format:
        [
        {{ "question": "...", "answer": "..." }},
        ...
        ]
        """


        try:
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                None,
                lambda: self.model.generate_content(
                    contents=[{"role": "user", "parts": [{"text": prompt}]}],
                    generation_config={"response_mime_type": "application/json", "temperature": 0.1}
                )
            )
            text = response.text.strip().replace("```json", "").replace("```", "")
            qa_pairs = json.loads(text)
            if isinstance(qa_pairs, list) and all('question' in qa and 'answer' in qa for qa in qa_pairs):
                return qa_pairs
            else:
                logger.warning("Response JSON does not match expected QA format.")
                return []
        except Exception as e:
            logger.error(f"Error generating questions: {e}", exc_info=True)
            return []

    async def summarize_relevance(self, main_summary: str, related_text: str) -> str:
        prompt = (
            "You are given two Nepali news summaries:\n\n"
            f"📰 Main News Summary:\n{main_summary}\n\n"
            f"📰 Potentially Related News:\n{related_text[:4000]}\n\n"
            "Carefully compare the two news articles. Only write a comparison if they clearly refer to the same brand, product line, or event. "
            "Avoid making up links if they are unrelated.\n\n"
            "Write 3 to 4 short bullet points in Nepali showing their similarities or differences.\n"
            "Each bullet point should be clear, concise, and fit in one line.\n\n"
            "Format:\n• Bullet 1\n• Bullet 2\n• Bullet 3"
        )

        try:
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                None,
                lambda: self.model.generate_content(
                    contents=[{"role": "user", "parts": [{"text": prompt}]}],
                    generation_config={"response_mime_type": "text/plain"}
                )
            )
            return response.text.strip()
        except Exception as e:
            logger.error(f"Error in summarize_relevance: {e}", exc_info=True)
            return "• सम्बन्ध तुलना गर्ने क्रममा त्रुटि भयो।"

    async def retrieve_related_docs_by_summary(self, summary: str, ref_post_id: str, top_k: int = 5) -> List[Dict[str, Any]]:

        embedding = self.embedding_model.get_text_embedding(summary)
        query = VectorStoreQuery(
            query_embedding=embedding,
            similarity_top_k=top_k * 10,
            filters=MetadataFilters(filters=[
                MetadataFilter(key="doc_type", value="content_chunk", operator="==")
            ])
        )
        result = self.index.vector_store.query(query)

        post_to_nodes = defaultdict(list)
        for node, score in zip(result.nodes or [], result.similarities or []):
            post_id = node.metadata.get("ref_post_id")
            if post_id and post_id != ref_post_id:
                node_with_score = NodeWithScore(node=node, score=score)
                post_to_nodes[post_id].append(node_with_score)

        top_posts = sorted(post_to_nodes.items(), key=lambda x: max(n.score for n in x[1]), reverse=True)[:top_k]

        results = []
        for post_id, nodes in top_posts:
            best_node = max(nodes, key=lambda n: n.score)
            results.append({
                "ref_post_id": post_id,
                "post_title" : best_node.node.metadata.get("title_nepali", ""),
                "node": best_node,
                "score": best_node.score,
                "url": best_node.node.metadata.get("post_url", "")
            })
        return results

    async def retrieve_full(self, post_id: str, mongo_uri: str, mongo_db: str, mongo_collection: str) -> Dict[str, Any]:
        try:
            # hashed_id = PostHasher.hash_post_id(post_id)
            hashed_id = post_id
            client = AsyncMongoClient(mongo_uri)
            collection = client[mongo_db][mongo_collection]

            docs_cursor = collection.find({"post_id": hashed_id})
            docs = await docs_cursor.to_list(length=None)
            if not docs:
                return {"main_post_summary": "• यो पोस्टको लागि कुनै डाटा भेटिएन।", "related": {}}

            segments = []
            for doc in docs:
                article = doc.get("article_content", {})
                for section in ["headline", "lead", "content", "main_story", "quotes", "reaction", "conclusion_or_call_to_action"]:
                    if section in article and article[section]:
                        segments.append({"section": section, "text": article[section]})

            main_summary = await self.summarize_segments(segments)
            related_nodes = await self.retrieve_related_docs_by_summary(main_summary, hashed_id)
            related_data = []
            for i, related in enumerate(related_nodes, 1):
                node_text = related["node"].node.get_content()
                relevance_summary = await self.summarize_relevance(main_summary, node_text)
                qa_pairs = await self.generate_questions(main_summary, node_text)

                related_data.append({
                    "post_title": related.get("post_title", ""),
                    "relevance_summary": relevance_summary,
                    "qa_pairs": qa_pairs,
                    "score": round(related["score"], 3),
                    "post_url": related.get("url", "")
                })
            return {
                "main_post_summary": main_summary,
                "related_posts": related_data
            }

        except Exception as e:
            logger.error(f"Error in retrieve_full: {str(e)}", exc_info=True)
            return {"success": False,
                "main_post_summary": f"• त्रुटि: {str(e)}", "related": {}}