# from typing import List, Optional
# from llama_index.core.vector_stores import MetadataFilter, MetadataFilters
# from .base_retriever import BaseRetriever
# from ..schemas import NodeWithScore, DocumentNode

# from ..config import RAGConfig
# import logging
# import asyncio

# logger = logging.getLogger(__name__)

# class AgenticRetriever(BaseRetriever):
#     def __init__(self):
#         """Initialize with config-provided index."""
#         try:
#             self.config = RAGConfig()
#             self.index = self.config.index
#             logger.info("AgenticRetriever initialized successfully")
#         except Exception as e:
#             logger.error(f"Failed to initialize AgenticRetriever: {e}")
#             raise

#     async def _get_relevant_categories(self, query: str, sample_size: int = 10) -> List[str]:
#         """Get relevant categories by sampling from Qdrant initial results."""
#         try:
#             # Do an initial broad search
#             initial_retriever = self.index.as_retriever(
#                 similarity_top_k=sample_size
#             )
#             initial_nodes = initial_retriever.retrieve(query)

#             # Extract unique categories from the results
#             categories = list(set(
#                 node.metadata.get('category') 
#                 for node in initial_nodes 
#                 if node.metadata.get('category')
#             ))

#             logger.info(f"Found {len(categories)} potential categories for query: {query}")
#             return categories

#         except Exception as e:
#             logger.error(f"Error finding categories: {e}")
#             return []

#     async def retrieve(self, query: str, top_k: int = 5) -> List[NodeWithScore]:
#         """
#         1. Sample documents to find relevant categories
#         2. Use categories to filter final search
#         3. Return nodes
        
#         Args:
#             query: Search query string
#             top_k: Number of documents to retrieve
        
#         Returns:
#             List of NodeWithScore objects
#         """
#         # Step 1: Get relevant categories from initial search
#         try:
#             categories = await self._get_relevant_categories(query)
#         except Exception as e:
#             logger.error(f"Category detection failed: {e}")
#             categories = None

#         # Step 2: Build proper Qdrant filters
#         qdrant_filters = None
#         if categories:
#             try:
#                 logger.info(f"Filtering by categories: {categories}")
#                 qdrant_filters = MetadataFilters(
#                     filters=[
#                         MetadataFilter(
#                             key="category",
#                             value=categories,
#                             operator="in" if isinstance(categories, list) else "eq"
#                         )
#                     ]
#                 )
#             except Exception as e:
#                 logger.error(f"Failed to build filters: {e}")
#                 qdrant_filters = None

#         # Step 3: Query Qdrant with proper filter handling
#         try:
#             retriever = self.index.as_retriever(
#                 similarity_top_k=top_k,
#                 filters=qdrant_filters
#             )
#             nodes = await asyncio.get_event_loop().run_in_executor(
#                 None,  # Uses default executor
#                 lambda: retriever.retrieve(query)
#             )
#         except Exception as e:
#             logger.error(f"Qdrant retrieval failed: {e}")
#             return []

#         # Step 4: Format results with error handling
#         result_nodes = []
#         for node in nodes:
#             try:
#                 result_nodes.append(NodeWithScore(
#                     node=DocumentNode(
#                         id_=node.node.id_ if hasattr(node, 'node') else node.id_,
#                         text=node.node.text if hasattr(node, 'node') else node.text,
#                         metadata=node.node.metadata if hasattr(node, 'node') else node.metadata,
#                         embedding=None
#                     ),
#                     score=node.score
#                 ))
#             except Exception as e:
#                 logger.warning(f"Skipping malformed node: {e}")
#                 continue

#         return result_nodes

#     def format_node(self, node: NodeWithScore) -> dict:
#         """Format a node for JSON-like output."""
#         return {
#             "id": node.node.id_,
#             "text": node.node.text,
#             "metadata": node.node.metadata,
#             "score": node.score,
#         }