from abc import ABC, abstractmethod
from typing import List, Dict, Any
from ..schemas import NodeWithScore

class BaseRetriever(ABC):
    # @abstractmethod
    # async def retrieve(self, query: str, top_k: int = 5) -> List[NodeWithScore]:
    #     """Standard vector-based retrieval using a query"""
    #     pass

    @abstractmethod
    async def retrieve_full(self, post_id: str) -> Dict[str, Any]:
        """Extended retrieval and processing logic using post_id"""
        pass

    # @abstractmethod
    # def format_node(self, node: NodeWithScore) -> dict:
    #     """Format a node for output"""
    #     pass
