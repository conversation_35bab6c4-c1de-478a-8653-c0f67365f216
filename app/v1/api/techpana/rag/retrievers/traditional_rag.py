# from typing import List, Dict, Any
# from llama_index.core import VectorStoreIndex
# from llama_index.embeddings.openai import Open<PERSON>IEmbedding
# from llama_index.vector_stores.qdrant import QdrantVectorStore
# from qdrant_client import QdrantClient
# from .base_retriever import BaseRetriever
# from ..schemas import NodeWithScore
# from ..config import RAGConfig
# import logging
# import asyncio

# logger = logging.getLogger(__name__)

# class TraditionalRetriever(BaseRetriever):
#     def __init__(self):
#         """Initialize traditional retriever with configuration."""
#         try:
#             # Load config with initialized components
#             self.config = RAGConfig()
#             self.index = self.config.index
#             logger.info("TraditionalRetriever initialized successfully")
#         except Exception as e:
#             logger.error(f"Failed to initialize TraditionalRetriever: {e}")
#             raise

#     def retrieve_nodes(self, query: str, top_k: int = 5) -> List[NodeWithScore]:
#         """Direct vector search, returns top_k nodes."""
#         if not self.index:
#             logger.error("Index not initialized.")
#             return []
#         try:
#             retriever = self.index.as_retriever(similarity_top_k=top_k)
#             nodes = retriever.retrieve(query)
#             return nodes
#         except Exception as e:
#             logger.error(f"Traditional retrieval failed: {e}")
#             return []

#     async def retrieve(self, query: str, top_k: int = 5) -> List[NodeWithScore]:
#         """Async interface for direct retrieval."""
#         try:
#             # Run sync operation in thread pool
#             return await asyncio.get_event_loop().run_in_executor(
#                 None, self.retrieve_nodes, query, top_k
#             )
#         except Exception as e:
#             logger.error(f"Async traditional retrieval failed: {e}")
#             return []

#     def format_node(self, node: NodeWithScore) -> Dict[str, Any]:
#         """Format node for output matching agentic retriever format."""
#         try:
#             return {
#                 "id": getattr(node.node, "id_", None),
#                 "text": getattr(node.node, "text", None),
#                 "metadata": {
#                     "document_id": node.node.metadata.get("document_id"),
#                     "ref_post_id": node.node.metadata.get("ref_post_id"),
#                     "category": node.node.metadata.get("category"),
#                     "title_nepali": node.node.metadata.get("title_nepali"),
#                     "post_url": node.node.metadata.get("post_url"),
#                     "section": node.node.metadata.get("section"),
#                     "level": node.node.metadata.get("level")
#                 },
#                 "score": float(node.score) if node.score else None
#             }
#         except AttributeError as e:
#             logger.error(f"Error formatting node: {e}")
#             return {
#                 "id": None,
#                 "text": None,
#                 "metadata": {},
#                 "score": None
#             }