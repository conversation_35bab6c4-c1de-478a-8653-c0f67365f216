from fastapi import APIRouter, HTTPException, Depends
from app.models.user import UserTenantDB
from app.v1.api.techpana.models.postidModel import PostIDInput
from app.v1.api.techpana.models.responseModel import GenerationOutput
from app.v1.api.techpana.core import shared_state
import logging
import traceback
from app.core.security import  get_tenant_info, require_permissions


logger = logging.getLogger(__name__)

router = APIRouter(prefix="", tags=["Response"])

@router.get("/get_report/{post_id}")
async def generate(post_id):
    """
    Generate summary + QA from a given post_id.
    """
    if not shared_state.generator:
        raise HTTPException(
            status_code=503,
            detail="Generator not initialized. Check server logs."
        )

    try:
        return await shared_state.generator.get_report_from_post_id(post_id)
    except Exception as e:
        logger.error(f"❌ Error in /generate: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=str(e))