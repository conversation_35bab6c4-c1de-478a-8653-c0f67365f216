from fastapi import FastAP<PERSON>, APIRouter, HTTPException, Depends
from pydantic import BaseModel
from typing import List, Optional, Any
from contextlib import asynccontextmanager
import traceback
import logging

# from app.v1.api.permissions import get_tenant_info
# from ....core.security import require_permissions
# from ....models.user import UserTenantDB
from models import postidModel, responseModel
from rag.config import RAGConfig
from rag.response_generator import ResponseGenerator


# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Shared state
rag_config: Optional[RAGConfig] = None
generator: Optional[ResponseGenerator] = None


# Lifespan for startup/shutdown logic
@asynccontextmanager
async def lifespan(app: FastAPI):
    global rag_config, generator
    try:
        logger.info("🚀 Starting RAG initialization...")
        
        # Initialize config first
        rag_config = RAGConfig()
        logger.info(f"✅ RAG Config initialized")
        logger.info(f"   - MongoDB URI: {rag_config.mongo_uri}")
        logger.info(f"   - Database: {rag_config.mongo_db_name}")
        logger.info(f"   - Collection: {rag_config.mongo_collection_name}")
        
        # Validate config values
        if not rag_config.mongo_uri:
            raise ValueError("MongoDB URI is not set in config")
        if not rag_config.mongo_db_name:
            raise ValueError("MongoDB database name is not set in config")
        if not rag_config.mongo_collection_name:
            raise ValueError("MongoDB collection name is not set in config")
        
        # Initialize generator
        logger.info("🔧 Initializing ResponseGenerator...")
        generator = ResponseGenerator(
            mongo_uri=rag_config.mongo_uri,
            mongo_db=rag_config.mongo_db_name,
            mongo_collection=rag_config.mongo_collection_name
        )
        logger.info("✅ Generator initialized successfully!")
        
    except Exception as e:
        logger.error(f"❌ Failed to initialize generator: {str(e)}")
        logger.error(f"❌ Full traceback: {traceback.format_exc()}")
        # Don't raise here - let the app start but handle gracefully in endpoints
        generator = None
    
    yield
    
    logger.info("🧹 Shutting down...")
    if generator:
        # Add any cleanup logic here if your ResponseGenerator has cleanup methods
        pass

# App definition
app = FastAPI(
    title="RAG Summary + QA API",
    description="Generate summaries and questions from MongoDB post chunks",
    version="1.0.0",
)

# Router
router = APIRouter(tags=["RAG"])

@router.post("/generate", response_model=responseModel.GenerationOutput , )

async def generate(data: postidModel.PostIDInput):
    if not generator:
        raise HTTPException(
            status_code=503, 
            detail="Generator not initialized. Check server logs for initialization errors."
        )
    try:
        return await generator.generate_from_post_id(data.post_id)
    except Exception as e:
        logger.error(f"❌ Error in generate endpoint: {str(e)}")
        logger.error(f"❌ Full traceback: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))
# Mount the router
app.include_router(router)