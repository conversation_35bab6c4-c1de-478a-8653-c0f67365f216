import asyncio
import os
import pymongo
import logging
from dotenv import load_dotenv

from rag.vector_store import VectorStoreManager
from rag.response_generator import ResponseGenerator
from rag.config import RAGConfig
from rag.pipeline.segmentation import NewsSegmenter
from rag.pipeline.preprocessor import PreProcessor
from rag.pipeline.categoricalnewsbuilder import CategoricalNewsBuilder


# Setup logging
logging.getLogger("httpx").setLevel(logging.WARNING)
logging.getLogger("qdrant_client").setLevel(logging.WARNING)
logging.getLogger("llama_index").setLevel(logging.WARNING)
logging.getLogger("openai").setLevel(logging.WARNING)

load_dotenv()
GEMINI_API_KEY = os.getenv("GOOGLE_API_KEY")
MONGODB_URI = os.getenv("MONGODB_URI")
MONGODB_DB_NAME = os.getenv("MONGODB_DB_NAME")
MONGODB_COLLECTION_NAME = os.getenv("MONGODB_COLLECTION_NAME")


def get_mongo_collection(uri, db_name, collection_name):
    client = pymongo.MongoClient(uri)
    return client[db_name][collection_name]


# def preprocess_and_segment_news(uri, db, collection):
#     if not GEMINI_API_KEY:
#         raise ValueError("GOOGLE_API_KEY is missing in .env")

#     # DATA_PATH = "data/raw/news_dump.csv"
#     mongo_collection = get_mongo_collection(uri, db, collection)

#     preprocessor = PreProcessor(DATA_PATH)
#     processed_df = preprocessor.preprocess()

#     segmenter = NewsSegmenter(processed_df, api_key=GEMINI_API_KEY)
#     segmented_df = segmenter.segment(
#         user_prompt=(
#             "You are a news article segmentation expert. Segment the content "
#             "into headline, lead, context, main story, quotes, reaction, and conclusion/call to action."
#         )
#     )

#     builder = CategoricalNewsBuilder(segmented_df)
#     structured_news_list = builder.build()

#     if structured_news_list:
#         mongo_collection.insert_many(structured_news_list)
#         print(f"Inserted {len(structured_news_list)} documents into MongoDB.")

#     return structured_news_list


# def ensure_news_in_mongo(uri, db, collection):
#     coll = get_mongo_collection(uri, db, collection)
#     if coll.count_documents({}) == 0:
#         print("MongoDB is empty. Running preprocessing...")
#         return preprocess_and_segment_news(uri, db, collection)
#     print("MongoDB already has data.")
#     return list(coll.find())


async def interactive_post_id_loop():
    generator = ResponseGenerator(
        mongo_uri=MONGODB_URI,
        mongo_db=MONGODB_DB_NAME,
        mongo_collection=MONGODB_COLLECTION_NAME
    )

    print("\nReady to run post_id → summary + QA flow.")
    print("Type `exit` to quit.\n")

    while True:
        post_id = input("Enter post_id: ").strip()
        if post_id.lower() == 'exit':
            print("Exiting.")
            break

        try:
            print("Processing...\n")
            result = await generator.generate_from_post_id(post_id=post_id)
            print("\n=== Summary ===")
            print(result.get("summary", "No summary generated."))

            print("\n=== Questions & Answers ===")
            print(result.get("qa_pairs"))
            # for item in result.get("qa_pairs", []):
            #     print(f"\nQ: {item['question']}\nA: {item['answer']}")

            print("\n" + "=" * 60 + "\n")

        except Exception as e:
            print(f"❌ Error: {e}")


async def main():
    # ensure_news_in_mongo(MONGODB_URI, MONGODB_DB_NAME, MONGODB_COLLECTION_NAME)

    # config = RAGConfig()
    # vector_store = VectorStoreManager(
    #     mongo_uri=MONGODB_URI,
    #     mongo_db=MONGODB_DB_NAME,
    #     mongo_collection=MONGODB_COLLECTION_NAME
    # )

    # success, _ = vector_store.setup_and_populate(force_recreate=False)
    # if not success:
    #     print("Vector store setup failed or no data found.")
    #     return
    print("✅ Vector store ready.\n")
    await interactive_post_id_loop()


if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    asyncio.run(main())
