version: '3.8'

services:
  app:
    build: .
    networks:
      - my_bridge_net
    deploy:
      replicas: 3  # Only works with docker swarm, ignore in normal compose

  nginx:
    image: nginx:alpine
    container_name: nginx_proxy
    ports:
      - "8010:80"
    volumes:
      - ./etc/nginx/conf.d/techpana-ai.nextai.asia.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - app
    networks:
      - my_bridge_net

networks:
  my_bridge_net:
    driver: bridge